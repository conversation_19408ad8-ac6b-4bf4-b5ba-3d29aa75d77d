# Ví dụ sử dụng ExcellentLibrary trong Robot Framework

Đây là tập hợp các ví dụ minh họa cách sử dụng thư viện ExcellentLibrary trong Robot Framework. ExcellentLibrary là một thư viện giúp làm việc với file Excel (định dạng XLSX) trong Robot Framework.

## Cài đặt

Để sử dụng ExcellentLibrary, bạn cần cài đặt nó thông qua pip:

```bash
pip install robotframework-excellentlibrary
```

## C<PERSON>u trúc thư mục

Thư mục này có cấu trúc như sau:

### Thư mục chính
- `/resources`: Chứa tất cả các file Excel được sử dụng trong các test case

### Các file test case chính
- `workbook_operations.robot`: <PERSON> họa các thao tác với workbook (tạo, mở, đóng, chuyển đổi)
- `sheet_operations.robot`: <PERSON> họ<PERSON> các thao tác với sheet (t<PERSON><PERSON>, x<PERSON><PERSON>, chuy<PERSON>n đổi)
- `read_write_operations.robot`: <PERSON> họ<PERSON> các thao tác đọc và ghi dữ liệu
- `complete_example.robot`: Ví dụ tổng hợp về cách sử dụng ExcellentLibrary trong một kịch bản thực tế

## Chạy các ví dụ

Để chạy các ví dụ, sử dụng lệnh sau:

```bash
robot workbook_operations.robot
robot sheet_operations.robot
robot read_write_operations.robot
robot complete_example.robot
```

## Các keyword chính trong ExcellentLibrary

### Thao tác với Workbook
- `Create Workbook`: Tạo một workbook mới
- `Open Workbook`: Mở một workbook có sẵn
- `Close Workbook`: Đóng một workbook
- `Close All Workbooks`: Đóng tất cả workbook đang mở (cẩn thận với lỗi "dictionary changed size during iteration")
- `Switch Workbook`: Chuyển đổi giữa các workbook
- `Save`: Lưu workbook hiện tại
- `Log Opened Workbooks`: Ghi log các workbook đang mở

### Thao tác với Sheet
- `Create Sheet`: Tạo một sheet mới
- `Remove Sheet`: Xóa một sheet
- `Switch Sheet`: Chuyển đổi giữa các sheet
- `Get Row Count`: Lấy số lượng hàng trong sheet hiện tại
- `Get Column Count`: Lấy số lượng cột trong sheet hiện tại
- `Get Row Iterator`: Lấy iterator để duyệt qua các hàng

### Đọc và ghi dữ liệu
- `Write To Cell`: Ghi dữ liệu vào một ô
- `Read From Cell`: Đọc dữ liệu từ một ô
- `Read Sheet Data`: Đọc toàn bộ dữ liệu từ một sheet

## Lưu ý

- ExcellentLibrary chỉ hỗ trợ file Excel định dạng XLSX (Excel 2010 trở lên), không hỗ trợ định dạng XLS cũ.
- Khi mở một workbook, bạn nên sử dụng đường dẫn tuyệt đối để tránh nhầm lẫn.
- Khi làm việc với nhiều workbook, nên sử dụng alias để dễ dàng tham chiếu.
- Các thay đổi chỉ được lưu khi bạn gọi keyword `Save`.
- Tránh sử dụng keyword `Close All Workbooks` vì có thể gây lỗi "dictionary changed size during iteration". Thay vào đó, hãy đóng từng workbook riêng lẻ bằng keyword `Close Workbook`.
