# Test Suite API CreateBooking

<PERSON><PERSON><PERSON> là bộ test suite Robot Framework để tự động hóa việc kiểm thử API CreateBooking của Restful Booker.

## Cấu trúc thư mục

- `create_booking_suite.robot`: File test suite chính
- `create_booking_data.robot`: Script để tạo file Excel chứa dữ liệu kiểm thử
- `resources/booking_data.xlsx`: File Excel chứa dữ liệu kiểm thử

## Cài đặt

Để chạy test suite này, bạn cần cài đặt các thư viện sau:

```bash
pip install robotframework
pip install robotframework-requests
pip install robotframework-excellentlibrary
pip install robotframework-jsonlibrary
```

## Tạo dữ liệu kiểm thử

Để tạo file Excel chứa dữ liệu kiểm thử, chạy lệnh sau:

```bash
robot create_booking_data.robot
```

Lệnh này sẽ tạo file `resources/booking_data.xlsx` với các dữ liệu mẫu.

## Chạy test suite

Để chạy test suite, sử dụng lệnh sau:

```bash
robot create_booking_suite.robot
```

## Cấu trúc test suite

### Test Cases

- `Create Booking From Excel Data`: Đọc dữ liệu từ Excel và tạo booking thông qua API

### Keywords

- `Create Booking`: Tạo booking thông qua API
- `Verify Booking Response`: Xác thực response từ API

## Cấu trúc dữ liệu Excel

File Excel `booking_data.xlsx` có các cột sau:

- `firstname`: Tên của khách hàng
- `lastname`: Họ của khách hàng
- `totalprice`: Tổng giá tiền
- `depositpaid`: Đã thanh toán đặt cọc hay chưa (TRUE/FALSE)
- `checkin_date`: Ngày nhận phòng (YYYY-MM-DD)
- `checkout_date`: Ngày trả phòng (YYYY-MM-DD)
- `additionalneeds`: Nhu cầu bổ sung

## API được kiểm thử

- **Endpoint**: https://restful-booker.herokuapp.com/booking
- **Method**: POST
- **Content-Type**: application/json
- **Accept**: application/json

## Cấu trúc JSON body

```json
{
    "firstname": "John",
    "lastname": "Smith",
    "totalprice": 150,
    "depositpaid": true,
    "bookingdates": {
        "checkin": "2023-12-01",
        "checkout": "2023-12-10"
    },
    "additionalneeds": "Breakfast"
}
```

## Xác thực

Test suite xác thực các điều kiện sau:

1. HTTP status code trả về là 200
2. Response body chứa `bookingid`
3. Response body chứa `booking` object
4. Các trường dữ liệu trong `booking` object khớp với dữ liệu đã gửi
