*** Settings ***
Library    ExcellentLibrary
Library    OperatingSystem
Library    Collections
Library    DateTime

*** Keywords ***
Clean Workbooks
    [Documentation]    Đ<PERSON>g tất cả workbook đang mở
    Run Keyword And Ignore Error    Close All Workbooks

*** Variables ***
${DATA_FILE}    ${CURDIR}${/}resources${/}employee_data.xlsx
${REPORT_FILE}  ${CURDIR}${/}resources${/}employee_report.xlsx

*** Test Cases ***
Create And Manipulate Excel Files
    [Documentation]    Ví dụ tổng hợp về cách sử dụng ExcellentLibrary
    Clean Workbooks

    # 1. Tạo file dữ liệu nhân viên
    Create Employee Data File

    # 2. Đọc dữ liệu từ file
    ${employee_data}=    Read Employee Data

    # 3. Tạo báo cáo dựa trên dữ liệu
    Create Employee Report    ${employee_data}

    # 4. Đóng từng workbook riêng lẻ
    Run Keyword And Ignore Error    Close Workbook    employee_data
    Run Keyword And Ignore Error    Close Workbook    employee_report

*** Keywords ***
Create Employee Data File
    [Documentation]    Tạo file Excel chứa dữ liệu nhân viên

    # Tạo workbook mới
    Create Workbook    ${DATA_FILE}    overwrite_file_if_exists=${TRUE}    alias=employee_data

    # Tạo dữ liệu tiêu đề
    Write To Cell    A1    ID
    Write To Cell    B1    Name
    Write To Cell    C1    Department
    Write To Cell    D1    Salary
    Write To Cell    E1    Hire Date

    # Thêm dữ liệu nhân viên
    Write To Cell    A2    1001
    Write To Cell    B2    John Smith
    Write To Cell    C2    IT
    Write To Cell    D2    ${5000}    number_format=#,##0.00
    ${date1}=    Convert Date    2020-01-15    result_format=datetime
    Write To Cell    E2    ${date1}    number_format=yyyy-mm-dd

    Write To Cell    A3    1002
    Write To Cell    B3    Alice Johnson
    Write To Cell    C3    HR
    Write To Cell    D3    ${4500}    number_format=#,##0.00
    ${date2}=    Convert Date    2019-05-20    result_format=datetime
    Write To Cell    E3    ${date2}    number_format=yyyy-mm-dd

    Write To Cell    A4    1003
    Write To Cell    B4    Bob Williams
    Write To Cell    C4    Finance
    Write To Cell    D4    ${6000}    number_format=#,##0.00
    ${date3}=    Convert Date    2021-03-10    result_format=datetime
    Write To Cell    E4    ${date3}    number_format=yyyy-mm-dd

    Write To Cell    A5    1004
    Write To Cell    B5    Emma Davis
    Write To Cell    C5    IT
    Write To Cell    D5    ${5200}    number_format=#,##0.00
    ${date4}=    Convert Date    2020-11-05    result_format=datetime
    Write To Cell    E5    ${date4}    number_format=yyyy-mm-dd

    # Lưu workbook
    Save

    # Log thông báo
    Log    Đã tạo file dữ liệu nhân viên thành công

Read Employee Data
    [Documentation]    Đọc dữ liệu nhân viên từ file Excel

    # Mở workbook dữ liệu nhân viên (nếu chưa mở)
    Run Keyword And Ignore Error    Switch Workbook    employee_data
    Run Keyword And Ignore Error    Open Workbook    ${DATA_FILE}    alias=employee_data

    # Đọc dữ liệu từ sheet với tên cột từ hàng đầu tiên
    ${employee_data}=    Read Sheet Data    get_column_names_from_header_row=${TRUE}

    # Log dữ liệu đã đọc
    Log    Đã đọc ${employee_data.__len__()} bản ghi nhân viên

    # Trả về dữ liệu
    RETURN    ${employee_data}

Create Employee Report
    [Documentation]    Tạo báo cáo nhân viên dựa trên dữ liệu
    [Arguments]    ${employee_data}

    # Tạo workbook báo cáo mới
    Create Workbook    ${REPORT_FILE}    overwrite_file_if_exists=${TRUE}    alias=employee_report

    # Tạo sheet cho báo cáo tổng hợp
    Create Sheet    Department Summary

    # Tạo tiêu đề cho sheet mặc định
    # Không cần Switch Sheet vì sheet mặc định đã active
    Write To Cell    A1    Employee Report
    Write To Cell    A2    Generated on:
    ${current_date}=    Get Current Date
    Write To Cell    B2    ${current_date}    number_format=yyyy-mm-dd hh:mm:ss

    # Sao chép dữ liệu nhân viên
    Write To Cell    A4    ID
    Write To Cell    B4    Name
    Write To Cell    C4    Department
    Write To Cell    D4    Salary
    Write To Cell    E4    Hire Date

    # Điền dữ liệu nhân viên
    ${row}=    Set Variable    5
    FOR    ${employee}    IN    @{employee_data}
        Write To Cell    A${row}    ${employee}[ID]
        Write To Cell    B${row}    ${employee}[Name]
        Write To Cell    C${row}    ${employee}[Department]
        Write To Cell    D${row}    ${employee}[Salary]    number_format=#,##0.00
        Write To Cell    E${row}    ${employee}[Hire Date]    number_format=yyyy-mm-dd
        ${row}=    Evaluate    ${row} + 1
    END

    # Chuyển sang sheet báo cáo tổng hợp
    Switch Sheet    Department Summary

    # Tạo tiêu đề cho báo cáo tổng hợp
    Write To Cell    A1    Department Summary Report
    Write To Cell    A3    Department
    Write To Cell    B3    Employee Count
    Write To Cell    C3    Average Salary

    # Tính toán số lượng nhân viên và lương trung bình theo phòng ban
    ${departments}=    Create Dictionary

    FOR    ${employee}    IN    @{employee_data}
        ${dept}=    Set Variable    ${employee}[Department]
        ${salary}=    Set Variable    ${employee}[Salary]

        # Kiểm tra xem phòng ban đã có trong từ điển chưa
        ${exists}=    Run Keyword And Return Status    Dictionary Should Contain Key    ${departments}    ${dept}

        IF    ${exists}
            ${dept_data}=    Get From Dictionary    ${departments}    ${dept}
            ${count}=    Set Variable    ${dept_data}[count]
            ${total_salary}=    Set Variable    ${dept_data}[total_salary]

            ${count}=    Evaluate    ${count} + 1
            ${total_salary}=    Evaluate    ${total_salary} + ${salary}

            ${new_dept_data}=    Create Dictionary    count=${count}    total_salary=${total_salary}
            Set To Dictionary    ${departments}    ${dept}=${new_dept_data}
        ELSE
            ${new_dept_data}=    Create Dictionary    count=1    total_salary=${salary}
            Set To Dictionary    ${departments}    ${dept}=${new_dept_data}
        END
    END

    # Điền dữ liệu tổng hợp vào báo cáo
    ${row}=    Set Variable    4
    FOR    ${dept}    ${data}    IN    &{departments}
        ${count}=    Set Variable    ${data}[count]
        ${total_salary}=    Set Variable    ${data}[total_salary]
        ${avg_salary}=    Evaluate    ${total_salary} / ${count}

        Write To Cell    A${row}    ${dept}
        Write To Cell    B${row}    ${count}
        Write To Cell    C${row}    ${avg_salary}    number_format=#,##0.00

        ${row}=    Evaluate    ${row} + 1
    END

    # Lưu workbook
    Save

    # Log thông báo
    Log    Đã tạo báo cáo nhân viên thành công
