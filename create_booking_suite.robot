*** Settings ***
Documentation    Test suite để kiểm thử API CreateBooking
Library    RequestsLibrary
Library    ExcellentLibrary
Library    Collections
Library    DateTime
Library    String
Library    JSONLibrary

*** Variables ***
${BASE_URL}    https://restful-booker.herokuapp.com
${BOOKING_ENDPOINT}    /booking
${EXCEL_FILE}    ${CURDIR}${/}resources${/}booking_data.xlsx
${HEADERS}    {"Content-Type": "application/json", "Accept": "application/json"}

*** Test Cases ***
Create Booking From Excel Data
    [Documentation]    Đọc dữ liệu từ Excel và tạo booking thông qua API

    # Mở file Excel
    Open Workbook    ${EXCEL_FILE}

    # Đọc dữ liệu từ Excel
    ${booking_data}=    Read Sheet Data    get_column_names_from_header_row=${TRUE}

    # Duyệt qua từng dòng dữ liệu
    FOR    ${booking}    IN    @{booking_data}
        # Log thông tin booking
        Log    Đang tạo booking cho: ${booking}[firstname] ${booking}[lastname]

        # Tạo booking
        ${response}=    Create Booking    ${booking}

        # Xá<PERSON> thực kết quả
        Verify Booking Response    ${response}    ${booking}
    END

    # Đóng workbook
    Close Workbook

*** Keywords ***
Create Booking
    [Documentation]    Tạo booking thông qua API
    [Arguments]    ${booking_data}

    # Chuyển đổi ngày tháng sang định dạng chuẩn
    ${checkin_date}=    Set Variable    ${booking_data}[checkin_date]
    ${checkout_date}=    Set Variable    ${booking_data}[checkout_date]

    # Tạo object bookingdates
    ${bookingdates}=    Create Dictionary    checkin=${checkin_date}    checkout=${checkout_date}

    # Tạo payload cho request
    ${payload}=    Create Dictionary
    ...    firstname=${booking_data}[firstname]
    ...    lastname=${booking_data}[lastname]
    ...    totalprice=${booking_data}[totalprice]
    ...    depositpaid=${booking_data}[depositpaid]
    ...    bookingdates=${bookingdates}
    ...    additionalneeds=${booking_data}[additionalneeds]

    # Convert payload to JSON string for logging
    ${payload_json}=    Evaluate    json.dumps($payload, indent=4)    json
    Log    Payload: ${payload_json}

    # Gửi request POST
    ${headers_dict}=    Evaluate    json.loads('''${HEADERS}''')    json
    ${response}=    POST    ${BASE_URL}${BOOKING_ENDPOINT}
    ...    json=${payload}
    ...    headers=${headers_dict}
    ...    expected_status=any

    # Log response
    Log    Response status: ${response.status_code}
    Log    Response body: ${response.text}

    RETURN    ${response}

Verify Booking Response
    [Documentation]    Xác thực response từ API
    [Arguments]    ${response}    ${booking_data}

    # Xác thực status code
    Should Be Equal As Strings    ${response.status_code}    200

    # Chuyển response body thành dictionary
    ${response_body}=    Set Variable    ${response.json()}

    # Xác thực bookingid tồn tại
    Dictionary Should Contain Key    ${response_body}    bookingid

    # Xác thực booking tồn tại
    Dictionary Should Contain Key    ${response_body}    booking

    # Lấy thông tin booking từ response
    ${booking}=    Set Variable    ${response_body}[booking]

    # Xác thực các trường dữ liệu
    Should Be Equal As Strings    ${booking}[firstname]    ${booking_data}[firstname]
    Should Be Equal As Strings    ${booking}[lastname]    ${booking_data}[lastname]
    Should Be Equal As Numbers    ${booking}[totalprice]    ${booking_data}[totalprice]
    Should Be Equal    ${booking}[depositpaid]    ${booking_data}[depositpaid]
    Should Be Equal As Strings    ${booking}[additionalneeds]    ${booking_data}[additionalneeds]

    # Xác thực bookingdates
    Dictionary Should Contain Key    ${booking}    bookingdates
    Dictionary Should Contain Key    ${booking}[bookingdates]    checkin
    Dictionary Should Contain Key    ${booking}[bookingdates]    checkout
    Should Be Equal As Strings    ${booking}[bookingdates][checkin]    ${booking_data}[checkin_date]
    Should Be Equal As Strings    ${booking}[bookingdates][checkout]    ${booking_data}[checkout_date]

    # Log kết quả xác thực
    Log    Xác thực thành công cho booking của: ${booking_data}[firstname] ${booking_data}[lastname]
