*** Settings ***
Documentation    Test suite để kiểm thử API CreateBooking
Library    RequestsLibrary
Library    ExcellentLibrary
Library    Collections
Library    DateTime
Library    String
Library    JSONLibrary
Suite Setup    Clean Workbooks
Suite Teardown    Clean Workbooks
Test Teardown    Clean Workbooks

*** Variables ***
${BASE_URL}    https://restful-booker.herokuapp.com
${BOOKING_ENDPOINT}    /booking
${EXCEL_FILE}    ${CURDIR}${/}resources${/}booking_data.xlsx
${HEADERS}    {"Content-Type": "application/json", "Accept": "application/json"}

*** Test Cases ***


Get Booking And Write To Excel
    [Documentation]    Lấy thông tin booking từ API và ghi vào Excel
    [Tags]    get-booking    excel-write

    # Lấy thông tin booking từ API
    ${booking_response}=    Get Booking By ID    1187

    # Xác thực response
    ${booking_data}=    Verify Get Booking Response    ${booking_response}

    # Ghi dữ liệu vào Excel
    Write Booking Data To Excel    ${booking_data}    1187

*** Keywords ***
Clean Workbooks
    [Documentation]    Đóng tất cả workbook đang mở
    Run Keyword And Ignore Error    Close All Workbooks



Get Booking By ID
    [Documentation]    Lấy thông tin booking theo ID từ API
    [Arguments]    ${booking_id}

    # Log thông tin request
    Log    Đang lấy thông tin booking với ID: ${booking_id}

    # Gửi request GET
    ${headers_dict}=    Evaluate    json.loads('''${HEADERS}''')    json
    ${response}=    GET    ${BASE_URL}${BOOKING_ENDPOINT}/${booking_id}
    ...    headers=${headers_dict}
    ...    expected_status=any

    # Log response
    Log    Response status: ${response.status_code}
    Log    Response body: ${response.text}

    RETURN    ${response}

Verify Get Booking Response
    [Documentation]    Xác thực response từ GET booking API
    [Arguments]    ${response}

    # Xác thực status code với error handling
    IF    ${response.status_code} == 404
        Fail    Booking không tồn tại (404 Not Found)
    ELSE IF    ${response.status_code} != 200
        Fail    API call thất bại với status code: ${response.status_code}
    END

    # Chuyển response body thành dictionary
    ${response_body}=    Set Variable    ${response.json()}

    # Xác thực các trường bắt buộc tồn tại
    Dictionary Should Contain Key    ${response_body}    firstname
    Dictionary Should Contain Key    ${response_body}    lastname
    Dictionary Should Contain Key    ${response_body}    totalprice
    Dictionary Should Contain Key    ${response_body}    depositpaid
    Dictionary Should Contain Key    ${response_body}    bookingdates
    Dictionary Should Contain Key    ${response_body}[bookingdates]    checkin
    Dictionary Should Contain Key    ${response_body}[bookingdates]    checkout

    # Log kết quả xác thực
    Log    Xác thực thành công cho booking: ${response_body}[firstname] ${response_body}[lastname]

    RETURN    ${response_body}

Write Booking Data To Excel
    [Documentation]    Ghi dữ liệu booking vào Excel sheet mới
    [Arguments]    ${booking_response}    ${booking_id}

    # Mở file Excel
    Open Workbook    ${EXCEL_FILE}

    # Tạo sheet mới với tên "bookingid" hoặc chuyển đến sheet đã tồn tại
    TRY
        Create Sheet    bookingid
        Log    Đã tạo sheet mới: bookingid
        # Sau khi tạo sheet mới, nó sẽ tự động trở thành sheet active
    EXCEPT    AS    ${error}
        Log    Sheet bookingid có thể đã tồn tại: ${error}
        # Chuyển đến sheet bookingid nếu đã tồn tại
        Switch Sheet    bookingid
        Log    Đã chuyển đến sheet: bookingid
    END

    # Log để debug - kiểm tra sheet hiện tại
    Log    Bắt đầu ghi dữ liệu vào sheet bookingid

    # Ghi header cho sheet
    Write To Cell    A1    Field
    Write To Cell    B1    Value

    # Log để xác nhận header đã được ghi
    Log    Đã ghi header vào sheet

    # Ghi dữ liệu booking
    ${row}=    Set Variable    2
    Write To Cell    A${row}    Booking ID
    Write To Cell    B${row}    ${booking_id}

    ${row}=    Evaluate    ${row} + 1
    Write To Cell    A${row}    First Name
    Write To Cell    B${row}    ${booking_response}[firstname]

    ${row}=    Evaluate    ${row} + 1
    Write To Cell    A${row}    Last Name
    Write To Cell    B${row}    ${booking_response}[lastname]

    ${row}=    Evaluate    ${row} + 1
    Write To Cell    A${row}    Total Price
    Write To Cell    B${row}    ${booking_response}[totalprice]

    ${row}=    Evaluate    ${row} + 1
    Write To Cell    A${row}    Deposit Paid
    Write To Cell    B${row}    ${booking_response}[depositpaid]

    ${row}=    Evaluate    ${row} + 1
    Write To Cell    A${row}    Check-in Date
    Write To Cell    B${row}    ${booking_response}[bookingdates][checkin]

    ${row}=    Evaluate    ${row} + 1
    Write To Cell    A${row}    Check-out Date
    Write To Cell    B${row}    ${booking_response}[bookingdates][checkout]

    # Ghi additional needs nếu có
    IF    'additionalneeds' in ${booking_response}
        ${row}=    Evaluate    ${row} + 1
        Write To Cell    A${row}    Additional Needs
        Write To Cell    B${row}    ${booking_response}[additionalneeds]
    END

    # Lưu workbook
    Save

    # Đóng workbook
    Close Workbook

    # Log kết quả
    Log    Đã ghi thành công dữ liệu booking ID ${booking_id} vào sheet 'bookingid'
