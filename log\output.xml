<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.10.11 on win32)" generated="2025-05-16T21:00:33.947102" rpa="false" schemaversion="5">
<suite id="s1" name="Create Booking Suite" source="C:\Users\<USER>\Documents\excel_robot_day5\create_booking_suite.robot">
<test id="s1-t1" name="Create Booking From Excel Data" line="17">
<kw name="Open Workbook" owner="ExcellentLibrary">
<arg>${EXCEL_FILE}</arg>
<doc>Opens the workbook found at the given file path.
_NOTE_: Please note that at present _XLS_ files are not supported.</doc>
<status status="PASS" start="2025-05-16T21:00:34.928484" elapsed="0.000000"/>
</kw>
<kw name="Read Sheet Data" owner="ExcellentLibrary">
<msg time="2025-05-16T21:00:34.944128" level="INFO">${booking_data} = [{'firstname': '<PERSON>', 'lastname': 'Smith', 'totalprice': 150, 'depositpaid': True, 'checkin_date': '2023-12-01', 'checkout_date': '2023-12-10', 'additionalneeds': 'Breakfast'}, {'firstname': 'Jane', ...</msg>
<var>${booking_data}</var>
<arg>get_column_names_from_header_row=${TRUE}</arg>
<doc>Reads all the data from the active sheet.</doc>
<status status="PASS" start="2025-05-16T21:00:34.928484" elapsed="0.015644"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:34.944128" level="INFO">Đang tạo booking cho: John Smith</msg>
<arg>Đang tạo booking cho: ${booking}[firstname] ${booking}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="0.000000"/>
</kw>
<kw name="Create Booking">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:34.944128" level="INFO">${checkin_date} = 2023-12-01</msg>
<var>${checkin_date}</var>
<arg>${booking_data}[checkin_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:34.944128" level="INFO">${checkout_date} = 2023-12-10</msg>
<var>${checkout_date}</var>
<arg>${booking_data}[checkout_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:34.944128" level="INFO">${bookingdates} = {'checkin': '2023-12-01', 'checkout': '2023-12-10'}</msg>
<var>${bookingdates}</var>
<arg>checkin=${checkin_date}</arg>
<arg>checkout=${checkout_date}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:34.944128" level="INFO">${payload} = {'firstname': 'John', 'lastname': 'Smith', 'totalprice': 150, 'depositpaid': True, 'bookingdates': {'checkin': '2023-12-01', 'checkout': '2023-12-10'}, 'additionalneeds': 'Breakfast'}</msg>
<var>${payload}</var>
<arg>firstname=${booking_data}[firstname]</arg>
<arg>lastname=${booking_data}[lastname]</arg>
<arg>totalprice=${booking_data}[totalprice]</arg>
<arg>depositpaid=${booking_data}[depositpaid]</arg>
<arg>bookingdates=${bookingdates}</arg>
<arg>additionalneeds=${booking_data}[additionalneeds]</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:34.944128" level="INFO">${payload_json} = {
    "firstname": "John",
    "lastname": "Smith",
    "totalprice": 150,
    "depositpaid": true,
    "bookingdates": {
        "checkin": "2023-12-01",
        "checkout": "2023-12-10"
    },
    "...</msg>
<var>${payload_json}</var>
<arg>json.dumps($payload, indent=4)</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:34.944128" level="INFO">Payload: {
    "firstname": "John",
    "lastname": "Smith",
    "totalprice": 150,
    "depositpaid": true,
    "bookingdates": {
        "checkin": "2023-12-01",
        "checkout": "2023-12-10"
    },
    "additionalneeds": "Breakfast"
}</msg>
<arg>Payload: ${payload_json}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:34.944128" level="INFO">${headers_dict} = {'Content-Type': 'application/json', 'Accept': 'application/json'}</msg>
<var>${headers_dict}</var>
<arg>json.loads('''${HEADERS}''')</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="0.000000"/>
</kw>
<kw name="POST" owner="RequestsLibrary">
<msg time="2025-05-16T21:00:36.475666" level="INFO">POST Request : url=https://restful-booker.herokuapp.com/booking 
 path_url=/booking 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Content-Length': '183'} 
 body=b'{"firstname": "John", "lastname": "Smith", "totalprice": 150, "depositpaid": true, "bookingdates": {"checkin": "2023-12-01", "checkout": "2023-12-10"}, "additionalneeds": "Breakfast"}' 
 </msg>
<msg time="2025-05-16T21:00:36.475666" level="INFO">POST Response : url=https://restful-booker.herokuapp.com/booking 
 status=200, reason=OK 
 headers={'Server': 'Cowboy', 'Report-To': '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1747404035&amp;sid=************************************&amp;s=B%2BU9rmkAvw5wGRSgLqGJPYWHPgOccKBvSD5uZyw75K8%3D"}]}', 'Reporting-Endpoints': 'heroku-nel=https://nel.heroku.com/reports?ts=1747404035&amp;sid=************************************&amp;s=B%2BU9rmkAvw5wGRSgLqGJPYWHPgOccKBvSD5uZyw75K8%3D', 'Nel': '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}', 'Connection': 'keep-alive', 'X-Powered-By': 'Express', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '198', 'Etag': 'W/"c6-H43n2fl3Fw1HU7XLkaCCtlqn+GQ"', 'Date': 'Fri, 16 May 2025 14:00:35 GMT', 'Via': '1.1 vegur'} 
 body={"bookingid":5210,"booking":{"firstname":"John","lastname":"Smith","totalprice":150,"depositpaid":true,"bookingdates":{"checkin":"2023-12-01","checkout":"2023-12-10"},"additionalneeds":"Breakfast"}} 
 </msg>
<msg time="2025-05-16T21:00:36.475666" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${BASE_URL}${BOOKING_ENDPOINT}</arg>
<arg>json=${payload}</arg>
<arg>headers=${headers_dict}</arg>
<arg>expected_status=any</arg>
<doc>Sends a POST request.</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="1.531538"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.475666" level="INFO">Response status: 200</msg>
<arg>Response status: ${response.status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.475666" level="INFO">Response body: {"bookingid":5210,"booking":{"firstname":"John","lastname":"Smith","totalprice":150,"depositpaid":true,"bookingdates":{"checkin":"2023-12-01","checkout":"2023-12-10"},"additionalneeds":"Breakfast"}}</msg>
<arg>Response body: ${response.text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<return>
<value>${response}</value>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</return>
<msg time="2025-05-16T21:00:36.475666" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${booking}</arg>
<doc>Tạo booking thông qua API</doc>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="1.531538"/>
</kw>
<kw name="Verify Booking Response">
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.475666" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<arg>${response.status_code}</arg>
<arg>200</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.475666" level="INFO">${response_body} = {'bookingid': 5210, 'booking': {'firstname': 'John', 'lastname': 'Smith', 'totalprice': 150, 'depositpaid': True, 'bookingdates': {'checkin': '2023-12-01', 'checkout': '2023-12-10'}, 'additionalneeds'...</msg>
<var>${response_body}</var>
<arg>${response.json()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>bookingid</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>booking</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.475666" level="INFO">${booking} = {'firstname': 'John', 'lastname': 'Smith', 'totalprice': 150, 'depositpaid': True, 'bookingdates': {'checkin': '2023-12-01', 'checkout': '2023-12-10'}, 'additionalneeds': 'Breakfast'}</msg>
<var>${booking}</var>
<arg>${response_body}[booking]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[firstname]</arg>
<arg>${booking_data}[firstname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[lastname]</arg>
<arg>${booking_data}[lastname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${booking}[totalprice]</arg>
<arg>${booking_data}[totalprice]</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${booking}[depositpaid]</arg>
<arg>${booking_data}[depositpaid]</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[additionalneeds]</arg>
<arg>${booking_data}[additionalneeds]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}</arg>
<arg>bookingdates</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkin</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkout</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkin]</arg>
<arg>${booking_data}[checkin_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkout]</arg>
<arg>${booking_data}[checkout_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">Xác thực thành công cho booking của: John Smith</msg>
<arg>Xác thực thành công cho booking của: ${booking_data}[firstname] ${booking_data}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<arg>${response}</arg>
<arg>${booking}</arg>
<doc>Xác thực response từ API</doc>
<status status="PASS" start="2025-05-16T21:00:36.475666" elapsed="0.015829"/>
</kw>
<var name="${booking}">{'firstname': 'John', 'lastname': 'Smith', 'totalprice': 150, 'depositpaid': True, 'checkin_date': '2023-12-01', 'checkout_date': '2023-12-10', 'additionalneeds': 'Breakfast'}</var>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="1.547367"/>
</iter>
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">Đang tạo booking cho: Jane Doe</msg>
<arg>Đang tạo booking cho: ${booking}[firstname] ${booking}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Create Booking">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">${checkin_date} = 2023-12-15</msg>
<var>${checkin_date}</var>
<arg>${booking_data}[checkin_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">${checkout_date} = 2023-12-20</msg>
<var>${checkout_date}</var>
<arg>${booking_data}[checkout_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">${bookingdates} = {'checkin': '2023-12-15', 'checkout': '2023-12-20'}</msg>
<var>${bookingdates}</var>
<arg>checkin=${checkin_date}</arg>
<arg>checkout=${checkout_date}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">${payload} = {'firstname': 'Jane', 'lastname': 'Doe', 'totalprice': 200, 'depositpaid': True, 'bookingdates': {'checkin': '2023-12-15', 'checkout': '2023-12-20'}, 'additionalneeds': 'Wifi'}</msg>
<var>${payload}</var>
<arg>firstname=${booking_data}[firstname]</arg>
<arg>lastname=${booking_data}[lastname]</arg>
<arg>totalprice=${booking_data}[totalprice]</arg>
<arg>depositpaid=${booking_data}[depositpaid]</arg>
<arg>bookingdates=${bookingdates}</arg>
<arg>additionalneeds=${booking_data}[additionalneeds]</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">${payload_json} = {
    "firstname": "Jane",
    "lastname": "Doe",
    "totalprice": 200,
    "depositpaid": true,
    "bookingdates": {
        "checkin": "2023-12-15",
        "checkout": "2023-12-20"
    },
    "ad...</msg>
<var>${payload_json}</var>
<arg>json.dumps($payload, indent=4)</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">Payload: {
    "firstname": "Jane",
    "lastname": "Doe",
    "totalprice": 200,
    "depositpaid": true,
    "bookingdates": {
        "checkin": "2023-12-15",
        "checkout": "2023-12-20"
    },
    "additionalneeds": "Wifi"
}</msg>
<arg>Payload: ${payload_json}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:36.491495" level="INFO">${headers_dict} = {'Content-Type': 'application/json', 'Accept': 'application/json'}</msg>
<var>${headers_dict}</var>
<arg>json.loads('''${HEADERS}''')</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="0.000000"/>
</kw>
<kw name="POST" owner="RequestsLibrary">
<msg time="2025-05-16T21:00:37.766322" level="INFO">POST Request : url=https://restful-booker.herokuapp.com/booking 
 path_url=/booking 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Content-Length': '176'} 
 body=b'{"firstname": "Jane", "lastname": "Doe", "totalprice": 200, "depositpaid": true, "bookingdates": {"checkin": "2023-12-15", "checkout": "2023-12-20"}, "additionalneeds": "Wifi"}' 
 </msg>
<msg time="2025-05-16T21:00:37.766322" level="INFO">POST Response : url=https://restful-booker.herokuapp.com/booking 
 status=200, reason=OK 
 headers={'Server': 'Cowboy', 'Report-To': '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1747404036&amp;sid=************************************&amp;s=EjjMSWwk7TifiqtxX%2BNZ3k31VaT0pNRRuYXQbt3MxZw%3D"}]}', 'Reporting-Endpoints': 'heroku-nel=https://nel.heroku.com/reports?ts=1747404036&amp;sid=************************************&amp;s=EjjMSWwk7TifiqtxX%2BNZ3k31VaT0pNRRuYXQbt3MxZw%3D', 'Nel': '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}', 'Connection': 'keep-alive', 'X-Powered-By': 'Express', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '191', 'Etag': 'W/"bf-mH6jg9TZGs5QGzwtLwm1HvsGxAs"', 'Date': 'Fri, 16 May 2025 14:00:36 GMT', 'Via': '1.1 vegur'} 
 body={"bookingid":5217,"booking":{"firstname":"Jane","lastname":"Doe","totalprice":200,"depositpaid":true,"bookingdates":{"checkin":"2023-12-15","checkout":"2023-12-20"},"additionalneeds":"Wifi"}} 
 </msg>
<msg time="2025-05-16T21:00:37.766322" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${BASE_URL}${BOOKING_ENDPOINT}</arg>
<arg>json=${payload}</arg>
<arg>headers=${headers_dict}</arg>
<arg>expected_status=any</arg>
<doc>Sends a POST request.</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="1.274827"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.766322" level="INFO">Response status: 200</msg>
<arg>Response status: ${response.status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.766322" level="INFO">Response body: {"bookingid":5217,"booking":{"firstname":"Jane","lastname":"Doe","totalprice":200,"depositpaid":true,"bookingdates":{"checkin":"2023-12-15","checkout":"2023-12-20"},"additionalneeds":"Wifi"}}</msg>
<arg>Response body: ${response.text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<return>
<value>${response}</value>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</return>
<msg time="2025-05-16T21:00:37.766322" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${booking}</arg>
<doc>Tạo booking thông qua API</doc>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="1.274827"/>
</kw>
<kw name="Verify Booking Response">
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.766322" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<arg>${response.status_code}</arg>
<arg>200</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.766322" level="INFO">${response_body} = {'bookingid': 5217, 'booking': {'firstname': 'Jane', 'lastname': 'Doe', 'totalprice': 200, 'depositpaid': True, 'bookingdates': {'checkin': '2023-12-15', 'checkout': '2023-12-20'}, 'additionalneeds': ...</msg>
<var>${response_body}</var>
<arg>${response.json()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>bookingid</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>booking</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.766322" level="INFO">${booking} = {'firstname': 'Jane', 'lastname': 'Doe', 'totalprice': 200, 'depositpaid': True, 'bookingdates': {'checkin': '2023-12-15', 'checkout': '2023-12-20'}, 'additionalneeds': 'Wifi'}</msg>
<var>${booking}</var>
<arg>${response_body}[booking]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[firstname]</arg>
<arg>${booking_data}[firstname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[lastname]</arg>
<arg>${booking_data}[lastname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${booking}[totalprice]</arg>
<arg>${booking_data}[totalprice]</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${booking}[depositpaid]</arg>
<arg>${booking_data}[depositpaid]</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[additionalneeds]</arg>
<arg>${booking_data}[additionalneeds]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}</arg>
<arg>bookingdates</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkin</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkout</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkin]</arg>
<arg>${booking_data}[checkin_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkout]</arg>
<arg>${booking_data}[checkout_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.766322" level="INFO">Xác thực thành công cho booking của: Jane Doe</msg>
<arg>Xác thực thành công cho booking của: ${booking_data}[firstname] ${booking_data}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<arg>${response}</arg>
<arg>${booking}</arg>
<doc>Xác thực response từ API</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<var name="${booking}">{'firstname': 'Jane', 'lastname': 'Doe', 'totalprice': 200, 'depositpaid': True, 'checkin_date': '2023-12-15', 'checkout_date': '2023-12-20', 'additionalneeds': 'Wifi'}</var>
<status status="PASS" start="2025-05-16T21:00:36.491495" elapsed="1.274827"/>
</iter>
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.766322" level="INFO">Đang tạo booking cho: Mike Brown</msg>
<arg>Đang tạo booking cho: ${booking}[firstname] ${booking}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.000000"/>
</kw>
<kw name="Create Booking">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.782447" level="INFO">${checkin_date} = 2024-01-05</msg>
<var>${checkin_date}</var>
<arg>${booking_data}[checkin_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="0.016125"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.782447" level="INFO">${checkout_date} = 2024-01-10</msg>
<var>${checkout_date}</var>
<arg>${booking_data}[checkout_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:37.782447" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.782447" level="INFO">${bookingdates} = {'checkin': '2024-01-05', 'checkout': '2024-01-10'}</msg>
<var>${bookingdates}</var>
<arg>checkin=${checkin_date}</arg>
<arg>checkout=${checkout_date}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:37.782447" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.782447" level="INFO">${payload} = {'firstname': 'Mike', 'lastname': 'Brown', 'totalprice': 100, 'depositpaid': False, 'bookingdates': {'checkin': '2024-01-05', 'checkout': '2024-01-10'}, 'additionalneeds': 'Extra pillow'}</msg>
<var>${payload}</var>
<arg>firstname=${booking_data}[firstname]</arg>
<arg>lastname=${booking_data}[lastname]</arg>
<arg>totalprice=${booking_data}[totalprice]</arg>
<arg>depositpaid=${booking_data}[depositpaid]</arg>
<arg>bookingdates=${bookingdates}</arg>
<arg>additionalneeds=${booking_data}[additionalneeds]</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:37.782447" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.782447" level="INFO">${payload_json} = {
    "firstname": "Mike",
    "lastname": "Brown",
    "totalprice": 100,
    "depositpaid": false,
    "bookingdates": {
        "checkin": "2024-01-05",
        "checkout": "2024-01-10"
    },
    ...</msg>
<var>${payload_json}</var>
<arg>json.dumps($payload, indent=4)</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:37.782447" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.782447" level="INFO">Payload: {
    "firstname": "Mike",
    "lastname": "Brown",
    "totalprice": 100,
    "depositpaid": false,
    "bookingdates": {
        "checkin": "2024-01-05",
        "checkout": "2024-01-10"
    },
    "additionalneeds": "Extra pillow"
}</msg>
<arg>Payload: ${payload_json}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:37.782447" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:37.782447" level="INFO">${headers_dict} = {'Content-Type': 'application/json', 'Accept': 'application/json'}</msg>
<var>${headers_dict}</var>
<arg>json.loads('''${HEADERS}''')</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:37.782447" elapsed="0.000000"/>
</kw>
<kw name="POST" owner="RequestsLibrary">
<msg time="2025-05-16T21:00:39.072972" level="INFO">POST Request : url=https://restful-booker.herokuapp.com/booking 
 path_url=/booking 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Content-Length': '187'} 
 body=b'{"firstname": "Mike", "lastname": "Brown", "totalprice": 100, "depositpaid": false, "bookingdates": {"checkin": "2024-01-05", "checkout": "2024-01-10"}, "additionalneeds": "Extra pillow"}' 
 </msg>
<msg time="2025-05-16T21:00:39.072972" level="INFO">POST Response : url=https://restful-booker.herokuapp.com/booking 
 status=200, reason=OK 
 headers={'Server': 'Cowboy', 'Report-To': '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1747404038&amp;sid=************************************&amp;s=kME7xosxqBfG%2FNlLBaIOBlQLkNUlroxlKy2HRtzwx2o%3D"}]}', 'Reporting-Endpoints': 'heroku-nel=https://nel.heroku.com/reports?ts=1747404038&amp;sid=************************************&amp;s=kME7xosxqBfG%2FNlLBaIOBlQLkNUlroxlKy2HRtzwx2o%3D', 'Nel': '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}', 'Connection': 'keep-alive', 'X-Powered-By': 'Express', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '202', 'Etag': 'W/"ca-NKOlqqANqp73nbsaWvvDrwk/u94"', 'Date': 'Fri, 16 May 2025 14:00:38 GMT', 'Via': '1.1 vegur'} 
 body={"bookingid":5224,"booking":{"firstname":"Mike","lastname":"Brown","totalprice":100,"depositpaid":false,"bookingdates":{"checkin":"2024-01-05","checkout":"2024-01-10"},"additionalneeds":"Extra pillow"}} 
 </msg>
<msg time="2025-05-16T21:00:39.072972" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${BASE_URL}${BOOKING_ENDPOINT}</arg>
<arg>json=${payload}</arg>
<arg>headers=${headers_dict}</arg>
<arg>expected_status=any</arg>
<doc>Sends a POST request.</doc>
<status status="PASS" start="2025-05-16T21:00:37.782447" elapsed="1.290525"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.072972" level="INFO">Response status: 200</msg>
<arg>Response status: ${response.status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:39.072972" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.089167" level="INFO">Response body: {"bookingid":5224,"booking":{"firstname":"Mike","lastname":"Brown","totalprice":100,"depositpaid":false,"bookingdates":{"checkin":"2024-01-05","checkout":"2024-01-10"},"additionalneeds":"Extra pillow"}}</msg>
<arg>Response body: ${response.text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:39.072972" elapsed="0.016195"/>
</kw>
<return>
<value>${response}</value>
<status status="PASS" start="2025-05-16T21:00:39.089167" elapsed="0.000000"/>
</return>
<msg time="2025-05-16T21:00:39.089167" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${booking}</arg>
<doc>Tạo booking thông qua API</doc>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="1.322845"/>
</kw>
<kw name="Verify Booking Response">
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.091106" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<arg>${response.status_code}</arg>
<arg>200</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:39.089167" elapsed="0.001939"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.093111" level="INFO">${response_body} = {'bookingid': 5224, 'booking': {'firstname': 'Mike', 'lastname': 'Brown', 'totalprice': 100, 'depositpaid': False, 'bookingdates': {'checkin': '2024-01-05', 'checkout': '2024-01-10'}, 'additionalneeds...</msg>
<var>${response_body}</var>
<arg>${response.json()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:39.091106" elapsed="0.002005"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>bookingid</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:39.093111" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>booking</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:39.093111" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.095115" level="INFO">${booking} = {'firstname': 'Mike', 'lastname': 'Brown', 'totalprice': 100, 'depositpaid': False, 'bookingdates': {'checkin': '2024-01-05', 'checkout': '2024-01-10'}, 'additionalneeds': 'Extra pillow'}</msg>
<var>${booking}</var>
<arg>${response_body}[booking]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:39.093111" elapsed="0.002004"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[firstname]</arg>
<arg>${booking_data}[firstname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:39.095115" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[lastname]</arg>
<arg>${booking_data}[lastname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:39.095115" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${booking}[totalprice]</arg>
<arg>${booking_data}[totalprice]</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2025-05-16T21:00:39.095115" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${booking}[depositpaid]</arg>
<arg>${booking_data}[depositpaid]</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-05-16T21:00:39.095115" elapsed="0.002002"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[additionalneeds]</arg>
<arg>${booking_data}[additionalneeds]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:39.097117" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}</arg>
<arg>bookingdates</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:39.097117" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkin</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:39.097117" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkout</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:39.097117" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkin]</arg>
<arg>${booking_data}[checkin_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:39.097117" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkout]</arg>
<arg>${booking_data}[checkout_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:39.097117" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.097117" level="INFO">Xác thực thành công cho booking của: Mike Brown</msg>
<arg>Xác thực thành công cho booking của: ${booking_data}[firstname] ${booking_data}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:39.097117" elapsed="0.002002"/>
</kw>
<arg>${response}</arg>
<arg>${booking}</arg>
<doc>Xác thực response từ API</doc>
<status status="PASS" start="2025-05-16T21:00:39.089167" elapsed="0.009952"/>
</kw>
<var name="${booking}">{'firstname': 'Mike', 'lastname': 'Brown', 'totalprice': 100, 'depositpaid': False, 'checkin_date': '2024-01-05', 'checkout_date': '2024-01-10', 'additionalneeds': 'Extra pillow'}</var>
<status status="PASS" start="2025-05-16T21:00:37.766322" elapsed="1.332797"/>
</iter>
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.099119" level="INFO">Đang tạo booking cho: Sarah Johnson</msg>
<arg>Đang tạo booking cho: ${booking}[firstname] ${booking}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:39.099119" elapsed="0.000000"/>
</kw>
<kw name="Create Booking">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.099119" level="INFO">${checkin_date} = 2024-02-01</msg>
<var>${checkin_date}</var>
<arg>${booking_data}[checkin_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:39.099119" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.101121" level="INFO">${checkout_date} = 2024-02-15</msg>
<var>${checkout_date}</var>
<arg>${booking_data}[checkout_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:39.099119" elapsed="0.002002"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.101121" level="INFO">${bookingdates} = {'checkin': '2024-02-01', 'checkout': '2024-02-15'}</msg>
<var>${bookingdates}</var>
<arg>checkin=${checkin_date}</arg>
<arg>checkout=${checkout_date}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:39.101121" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.101121" level="INFO">${payload} = {'firstname': 'Sarah', 'lastname': 'Johnson', 'totalprice': 300, 'depositpaid': True, 'bookingdates': {'checkin': '2024-02-01', 'checkout': '2024-02-15'}, 'additionalneeds': 'Late checkout'}</msg>
<var>${payload}</var>
<arg>firstname=${booking_data}[firstname]</arg>
<arg>lastname=${booking_data}[lastname]</arg>
<arg>totalprice=${booking_data}[totalprice]</arg>
<arg>depositpaid=${booking_data}[depositpaid]</arg>
<arg>bookingdates=${bookingdates}</arg>
<arg>additionalneeds=${booking_data}[additionalneeds]</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:39.101121" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.101121" level="INFO">${payload_json} = {
    "firstname": "Sarah",
    "lastname": "Johnson",
    "totalprice": 300,
    "depositpaid": true,
    "bookingdates": {
        "checkin": "2024-02-01",
        "checkout": "2024-02-15"
    },
  ...</msg>
<var>${payload_json}</var>
<arg>json.dumps($payload, indent=4)</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:39.101121" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.101121" level="INFO">Payload: {
    "firstname": "Sarah",
    "lastname": "Johnson",
    "totalprice": 300,
    "depositpaid": true,
    "bookingdates": {
        "checkin": "2024-02-01",
        "checkout": "2024-02-15"
    },
    "additionalneeds": "Late checkout"
}</msg>
<arg>Payload: ${payload_json}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:39.101121" elapsed="0.002002"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:39.103123" level="INFO">${headers_dict} = {'Content-Type': 'application/json', 'Accept': 'application/json'}</msg>
<var>${headers_dict}</var>
<arg>json.loads('''${HEADERS}''')</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:39.103123" elapsed="0.000000"/>
</kw>
<kw name="POST" owner="RequestsLibrary">
<msg time="2025-05-16T21:00:40.383155" level="INFO">POST Request : url=https://restful-booker.herokuapp.com/booking 
 path_url=/booking 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Content-Length': '190'} 
 body=b'{"firstname": "Sarah", "lastname": "Johnson", "totalprice": 300, "depositpaid": true, "bookingdates": {"checkin": "2024-02-01", "checkout": "2024-02-15"}, "additionalneeds": "Late checkout"}' 
 </msg>
<msg time="2025-05-16T21:00:40.393066" level="INFO">POST Response : url=https://restful-booker.herokuapp.com/booking 
 status=200, reason=OK 
 headers={'Server': 'Cowboy', 'Report-To': '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1747404039&amp;sid=************************************&amp;s=kaDfsECHbisCnOHvucXVMPYgE1SzlbL1N%2F2gZFY5Iag%3D"}]}', 'Reporting-Endpoints': 'heroku-nel=https://nel.heroku.com/reports?ts=1747404039&amp;sid=************************************&amp;s=kaDfsECHbisCnOHvucXVMPYgE1SzlbL1N%2F2gZFY5Iag%3D', 'Nel': '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}', 'Connection': 'keep-alive', 'X-Powered-By': 'Express', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '205', 'Etag': 'W/"cd-fGEO/+LhHIeBAYXcp/mz42sOA6s"', 'Date': 'Fri, 16 May 2025 14:00:39 GMT', 'Via': '1.1 vegur'} 
 body={"bookingid":5236,"booking":{"firstname":"Sarah","lastname":"Johnson","totalprice":300,"depositpaid":true,"bookingdates":{"checkin":"2024-02-01","checkout":"2024-02-15"},"additionalneeds":"Late checkout"}} 
 </msg>
<msg time="2025-05-16T21:00:40.393066" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${BASE_URL}${BOOKING_ENDPOINT}</arg>
<arg>json=${payload}</arg>
<arg>headers=${headers_dict}</arg>
<arg>expected_status=any</arg>
<doc>Sends a POST request.</doc>
<status status="PASS" start="2025-05-16T21:00:39.103123" elapsed="1.289943"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.395073" level="INFO">Response status: 200</msg>
<arg>Response status: ${response.status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:40.393066" elapsed="0.002007"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.397080" level="INFO">Response body: {"bookingid":5236,"booking":{"firstname":"Sarah","lastname":"Johnson","totalprice":300,"depositpaid":true,"bookingdates":{"checkin":"2024-02-01","checkout":"2024-02-15"},"additionalneeds":"Late checkout"}}</msg>
<arg>Response body: ${response.text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:40.395073" elapsed="0.002007"/>
</kw>
<return>
<value>${response}</value>
<status status="PASS" start="2025-05-16T21:00:40.397080" elapsed="0.000000"/>
</return>
<msg time="2025-05-16T21:00:40.397080" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${booking}</arg>
<doc>Tạo booking thông qua API</doc>
<status status="PASS" start="2025-05-16T21:00:39.099119" elapsed="1.297961"/>
</kw>
<kw name="Verify Booking Response">
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<arg>${response.status_code}</arg>
<arg>200</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">${response_body} = {'bookingid': 5236, 'booking': {'firstname': 'Sarah', 'lastname': 'Johnson', 'totalprice': 300, 'depositpaid': True, 'bookingdates': {'checkin': '2024-02-01', 'checkout': '2024-02-15'}, 'additionalnee...</msg>
<var>${response_body}</var>
<arg>${response.json()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>bookingid</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>booking</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">${booking} = {'firstname': 'Sarah', 'lastname': 'Johnson', 'totalprice': 300, 'depositpaid': True, 'bookingdates': {'checkin': '2024-02-01', 'checkout': '2024-02-15'}, 'additionalneeds': 'Late checkout'}</msg>
<var>${booking}</var>
<arg>${response_body}[booking]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[firstname]</arg>
<arg>${booking_data}[firstname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[lastname]</arg>
<arg>${booking_data}[lastname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${booking}[totalprice]</arg>
<arg>${booking_data}[totalprice]</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${booking}[depositpaid]</arg>
<arg>${booking_data}[depositpaid]</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[additionalneeds]</arg>
<arg>${booking_data}[additionalneeds]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}</arg>
<arg>bookingdates</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkin</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkout</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkin]</arg>
<arg>${booking_data}[checkin_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkout]</arg>
<arg>${booking_data}[checkout_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">Xác thực thành công cho booking của: Sarah Johnson</msg>
<arg>Xác thực thành công cho booking của: ${booking_data}[firstname] ${booking_data}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<arg>${response}</arg>
<arg>${booking}</arg>
<doc>Xác thực response từ API</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<var name="${booking}">{'firstname': 'Sarah', 'lastname': 'Johnson', 'totalprice': 300, 'depositpaid': True, 'checkin_date': '2024-02-01', 'checkout_date': '2024-02-15', 'additionalneeds': 'Late checkout'}</var>
<status status="PASS" start="2025-05-16T21:00:39.099119" elapsed="1.299707"/>
</iter>
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">Đang tạo booking cho: Robert Williams</msg>
<arg>Đang tạo booking cho: ${booking}[firstname] ${booking}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Create Booking">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">${checkin_date} = 2024-03-10</msg>
<var>${checkin_date}</var>
<arg>${booking_data}[checkin_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">${checkout_date} = 2024-03-15</msg>
<var>${checkout_date}</var>
<arg>${booking_data}[checkout_date]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">${bookingdates} = {'checkin': '2024-03-10', 'checkout': '2024-03-15'}</msg>
<var>${bookingdates}</var>
<arg>checkin=${checkin_date}</arg>
<arg>checkout=${checkout_date}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">${payload} = {'firstname': 'Robert', 'lastname': 'Williams', 'totalprice': 250, 'depositpaid': False, 'bookingdates': {'checkin': '2024-03-10', 'checkout': '2024-03-15'}, 'additionalneeds': 'Airport transfer'}</msg>
<var>${payload}</var>
<arg>firstname=${booking_data}[firstname]</arg>
<arg>lastname=${booking_data}[lastname]</arg>
<arg>totalprice=${booking_data}[totalprice]</arg>
<arg>depositpaid=${booking_data}[depositpaid]</arg>
<arg>bookingdates=${bookingdates}</arg>
<arg>additionalneeds=${booking_data}[additionalneeds]</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">${payload_json} = {
    "firstname": "Robert",
    "lastname": "Williams",
    "totalprice": 250,
    "depositpaid": false,
    "bookingdates": {
        "checkin": "2024-03-10",
        "checkout": "2024-03-15"
    },...</msg>
<var>${payload_json}</var>
<arg>json.dumps($payload, indent=4)</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">Payload: {
    "firstname": "Robert",
    "lastname": "Williams",
    "totalprice": 250,
    "depositpaid": false,
    "bookingdates": {
        "checkin": "2024-03-10",
        "checkout": "2024-03-15"
    },
    "additionalneeds": "Airport transfer"
}</msg>
<arg>Payload: ${payload_json}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-16T21:00:40.398826" level="INFO">${headers_dict} = {'Content-Type': 'application/json', 'Accept': 'application/json'}</msg>
<var>${headers_dict}</var>
<arg>json.loads('''${HEADERS}''')</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="0.000000"/>
</kw>
<kw name="POST" owner="RequestsLibrary">
<msg time="2025-05-16T21:00:43.406628" level="INFO">POST Request : url=https://restful-booker.herokuapp.com/booking 
 path_url=/booking 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Content-Length': '196'} 
 body=b'{"firstname": "Robert", "lastname": "Williams", "totalprice": 250, "depositpaid": false, "bookingdates": {"checkin": "2024-03-10", "checkout": "2024-03-15"}, "additionalneeds": "Airport transfer"}' 
 </msg>
<msg time="2025-05-16T21:00:43.406628" level="INFO">POST Response : url=https://restful-booker.herokuapp.com/booking 
 status=200, reason=OK 
 headers={'Server': 'Cowboy', 'Report-To': '{"group":"heroku-nel","max_age":3600,"endpoints":[{"url":"https://nel.heroku.com/reports?ts=1747404042&amp;sid=************************************&amp;s=l%2Bc01RGHGHaPwMlBnUXlfZC%2FOkMvIk0Lb1502SZALzM%3D"}]}', 'Reporting-Endpoints': 'heroku-nel=https://nel.heroku.com/reports?ts=1747404042&amp;sid=************************************&amp;s=l%2Bc01RGHGHaPwMlBnUXlfZC%2FOkMvIk0Lb1502SZALzM%3D', 'Nel': '{"report_to":"heroku-nel","max_age":3600,"success_fraction":0.005,"failure_fraction":0.05,"response_headers":["Via"]}', 'Connection': 'keep-alive', 'X-Powered-By': 'Express', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '211', 'Etag': 'W/"d3-Df/gXzC57NGIgaO9ThPmEh36fLo"', 'Date': 'Fri, 16 May 2025 14:00:42 GMT', 'Via': '1.1 vegur'} 
 body={"bookingid":5261,"booking":{"firstname":"Robert","lastname":"Williams","totalprice":250,"depositpaid":false,"bookingdates":{"checkin":"2024-03-10","checkout":"2024-03-15"},"additionalneeds":"Airport transfer"}} 
 </msg>
<msg time="2025-05-16T21:00:43.415339" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${BASE_URL}${BOOKING_ENDPOINT}</arg>
<arg>json=${payload}</arg>
<arg>headers=${headers_dict}</arg>
<arg>expected_status=any</arg>
<doc>Sends a POST request.</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="3.016513"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:43.417347" level="INFO">Response status: 200</msg>
<arg>Response status: ${response.status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:43.415339" elapsed="0.002008"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:43.417347" level="INFO">Response body: {"bookingid":5261,"booking":{"firstname":"Robert","lastname":"Williams","totalprice":250,"depositpaid":false,"bookingdates":{"checkin":"2024-03-10","checkout":"2024-03-15"},"additionalneeds":"Airport transfer"}}</msg>
<arg>Response body: ${response.text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:43.417347" elapsed="0.000000"/>
</kw>
<return>
<value>${response}</value>
<status status="PASS" start="2025-05-16T21:00:43.419351" elapsed="0.000000"/>
</return>
<msg time="2025-05-16T21:00:43.419351" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${booking}</arg>
<doc>Tạo booking thông qua API</doc>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="3.020525"/>
</kw>
<kw name="Verify Booking Response">
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<msg time="2025-05-16T21:00:43.419351" level="INFO">Argument types are:
&lt;class 'int'&gt;
&lt;class 'str'&gt;</msg>
<arg>${response.status_code}</arg>
<arg>200</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:43.419351" elapsed="0.002002"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:43.421353" level="INFO">${response_body} = {'bookingid': 5261, 'booking': {'firstname': 'Robert', 'lastname': 'Williams', 'totalprice': 250, 'depositpaid': False, 'bookingdates': {'checkin': '2024-03-10', 'checkout': '2024-03-15'}, 'additional...</msg>
<var>${response_body}</var>
<arg>${response.json()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:43.421353" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>bookingid</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:43.421353" elapsed="0.001003"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>booking</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:43.422356" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-16T21:00:43.422356" level="INFO">${booking} = {'firstname': 'Robert', 'lastname': 'Williams', 'totalprice': 250, 'depositpaid': False, 'bookingdates': {'checkin': '2024-03-10', 'checkout': '2024-03-15'}, 'additionalneeds': 'Airport transfer'}</msg>
<var>${booking}</var>
<arg>${response_body}[booking]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-16T21:00:43.422356" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[firstname]</arg>
<arg>${booking_data}[firstname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:43.422356" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[lastname]</arg>
<arg>${booking_data}[lastname]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:43.422356" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${booking}[totalprice]</arg>
<arg>${booking_data}[totalprice]</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2025-05-16T21:00:43.422356" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${booking}[depositpaid]</arg>
<arg>${booking_data}[depositpaid]</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-05-16T21:00:43.422356" elapsed="0.002002"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[additionalneeds]</arg>
<arg>${booking_data}[additionalneeds]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:43.424358" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}</arg>
<arg>bookingdates</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:43.424358" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkin</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:43.424358" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${booking}[bookingdates]</arg>
<arg>checkout</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-16T21:00:43.424358" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkin]</arg>
<arg>${booking_data}[checkin_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:43.424358" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${booking}[bookingdates][checkout]</arg>
<arg>${booking_data}[checkout_date]</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2025-05-16T21:00:43.424358" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:00:43.426360" level="INFO">Xác thực thành công cho booking của: Robert Williams</msg>
<arg>Xác thực thành công cho booking của: ${booking_data}[firstname] ${booking_data}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:00:43.424358" elapsed="0.002002"/>
</kw>
<arg>${response}</arg>
<arg>${booking}</arg>
<doc>Xác thực response từ API</doc>
<status status="PASS" start="2025-05-16T21:00:43.419351" elapsed="0.007009"/>
</kw>
<var name="${booking}">{'firstname': 'Robert', 'lastname': 'Williams', 'totalprice': 250, 'depositpaid': False, 'checkin_date': '2024-03-10', 'checkout_date': '2024-03-15', 'additionalneeds': 'Airport transfer'}</var>
<status status="PASS" start="2025-05-16T21:00:40.398826" elapsed="3.027534"/>
</iter>
<var>${booking}</var>
<value>@{booking_data}</value>
<status status="PASS" start="2025-05-16T21:00:34.944128" elapsed="8.482232"/>
</for>
<kw name="Close Workbook" owner="ExcellentLibrary">
<doc>Closes the workbook identified by the supplied alias.</doc>
<status status="PASS" start="2025-05-16T21:00:43.426360" elapsed="0.000000"/>
</kw>
<doc>Đọc dữ liệu từ Excel và tạo booking thông qua API</doc>
<status status="PASS" start="2025-05-16T21:00:34.928484" elapsed="8.497876"/>
</test>
<doc>Test suite để kiểm thử API CreateBooking</doc>
<status status="PASS" start="2025-05-16T21:00:33.950110" elapsed="9.476250"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Create Booking Suite" id="s1" pass="1" fail="0" skip="0">Create Booking Suite</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
