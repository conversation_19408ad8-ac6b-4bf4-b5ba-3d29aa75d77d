<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.10.11 on win32)" generated="2025-05-28T18:01:02.613778" rpa="false" schemaversion="5">
<suite id="s1" name="Create Booking Suite" source="C:\Users\<USER>\Documents\excel_robot_day5\create_booking_suite.robot">
<kw name="Clean Workbooks" type="SETUP">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Close All Workbooks" owner="ExcellentLibrary">
<doc>Closes all opened workbooks.</doc>
<status status="PASS" start="2025-05-28T18:01:03.109312" elapsed="0.000000"/>
</kw>
<arg>Close All Workbooks</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-05-28T18:01:03.108309" elapsed="0.001003"/>
</kw>
<doc><PERSON><PERSON><PERSON> tất cả workbook đang mở</doc>
<status status="PASS" start="2025-05-28T18:01:03.108309" elapsed="0.001003"/>
</kw>
<test id="s1-t1" name="Get Booking And Write To Excel" line="22">
<kw name="Get Booking By ID">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:03.110319" level="INFO">Đang lấy thông tin booking với ID: 1187</msg>
<arg>Đang lấy thông tin booking với ID: ${booking_id}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:03.110319" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T18:01:03.110319" level="INFO">${headers_dict} = {'Content-Type': 'application/json', 'Accept': 'application/json'}</msg>
<var>${headers_dict}</var>
<arg>json.loads('''${HEADERS}''')</arg>
<arg>json</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T18:01:03.110319" elapsed="0.000000"/>
</kw>
<kw name="GET" owner="RequestsLibrary">
<msg time="2025-05-28T18:01:04.308727" level="INFO">GET Request : url=https://restful-booker.herokuapp.com/booking/1187 
 path_url=/booking/1187 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': 'application/json', 'Connection': 'keep-alive', 'Content-Type': 'application/json'} 
 body=None 
 </msg>
<msg time="2025-05-28T18:01:04.308727" level="INFO">GET Response : url=https://restful-booker.herokuapp.com/booking/1187 
 status=200, reason=OK 
 headers={'Content-Length': '169', 'Content-Type': 'application/json; charset=utf-8', 'Date': 'Wed, 28 May 2025 11:01:03 GMT', 'Etag': 'W/"a9-pLno4M4jARCCH8k0BsLT0ALMqUE"', 'Nel': '{"report_to":"heroku-nel","response_headers":["Via"],"max_age":3600,"success_fraction":0.01,"failure_fraction":0.1}', 'Report-To': '{"group":"heroku-nel","endpoints":[{"url":"https://nel.heroku.com/reports?s=s8pUbgpl8aa4vQnMCwbC9yZbPj%2BCW6oxnshov8EDv7I%3D\\u0026sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add\\u0026ts=1748430063"}],"max_age":3600}', 'Reporting-Endpoints': 'heroku-nel="https://nel.heroku.com/reports?s=s8pUbgpl8aa4vQnMCwbC9yZbPj%2BCW6oxnshov8EDv7I%3D&amp;sid=c46efe9b-d3d2-4a0c-8c76-bfafa16c5add&amp;ts=1748430063"', 'Server': 'Heroku', 'Via': '1.1 heroku-router', 'X-Powered-By': 'Express'} 
 body={"firstname":"John","lastname":"Smith","totalprice":111,"depositpaid":true,"bookingdates":{"checkin":"2018-01-01","checkout":"2019-01-01"},"additionalneeds":"Breakfast"} 
 </msg>
<msg time="2025-05-28T18:01:04.308727" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>${BASE_URL}${BOOKING_ENDPOINT}/${booking_id}</arg>
<arg>headers=${headers_dict}</arg>
<arg>expected_status=any</arg>
<doc>Sends a GET request.</doc>
<status status="PASS" start="2025-05-28T18:01:03.110319" elapsed="1.198408"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.308727" level="INFO">Response status: 200</msg>
<arg>Response status: ${response.status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.308727" level="INFO">Response body: {"firstname":"John","lastname":"Smith","totalprice":111,"depositpaid":true,"bookingdates":{"checkin":"2018-01-01","checkout":"2019-01-01"},"additionalneeds":"Breakfast"}</msg>
<arg>Response body: ${response.text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<return>
<value>${response}</value>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</return>
<msg time="2025-05-28T18:01:04.308727" level="INFO">${booking_response} = &lt;Response [200]&gt;</msg>
<var>${booking_response}</var>
<arg>1187</arg>
<doc>Lấy thông tin booking theo ID từ API</doc>
<status status="PASS" start="2025-05-28T18:01:03.109312" elapsed="1.199415"/>
</kw>
<kw name="Verify Get Booking Response">
<if>
<branch type="IF" condition="${response.status_code} == 404">
<kw name="Fail" owner="BuiltIn">
<arg>Booking không tồn tại (404 Not Found)</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</branch>
<branch type="ELSE IF" condition="${response.status_code} != 200">
<kw name="Fail" owner="BuiltIn">
<arg>API call thất bại với status code: ${response.status_code}</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.308727" level="INFO">${response_body} = {'firstname': 'John', 'lastname': 'Smith', 'totalprice': 111, 'depositpaid': True, 'bookingdates': {'checkin': '2018-01-01', 'checkout': '2019-01-01'}, 'additionalneeds': 'Breakfast'}</msg>
<var>${response_body}</var>
<arg>${response.json()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>firstname</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>lastname</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>totalprice</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>depositpaid</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}</arg>
<arg>bookingdates</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}[bookingdates]</arg>
<arg>checkin</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<arg>${response_body}[bookingdates]</arg>
<arg>checkout</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.308727" level="INFO">Xác thực thành công cho booking: John Smith</msg>
<arg>Xác thực thành công cho booking: ${response_body}[firstname] ${response_body}[lastname]</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</kw>
<return>
<value>${response_body}</value>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.000000"/>
</return>
<msg time="2025-05-28T18:01:04.324613" level="INFO">${booking_data} = {'firstname': 'John', 'lastname': 'Smith', 'totalprice': 111, 'depositpaid': True, 'bookingdates': {'checkin': '2018-01-01', 'checkout': '2019-01-01'}, 'additionalneeds': 'Breakfast'}</msg>
<var>${booking_data}</var>
<arg>${booking_response}</arg>
<doc>Xác thực response từ GET booking API</doc>
<status status="PASS" start="2025-05-28T18:01:04.308727" elapsed="0.015886"/>
</kw>
<kw name="Write Booking Data To Excel">
<kw name="Open Workbook" owner="ExcellentLibrary">
<arg>${EXCEL_FILE}</arg>
<doc>Opens the workbook found at the given file path.
_NOTE_: Please note that at present _XLS_ files are not supported.</doc>
<status status="PASS" start="2025-05-28T18:01:04.324613" elapsed="0.006447"/>
</kw>
<try>
<branch type="TRY">
<kw name="Create Sheet" owner="ExcellentLibrary">
<msg time="2025-05-28T18:01:04.331060" level="FAIL">SheetExistsAlreadyException: sheet `bookingid' already exists.</msg>
<arg>bookingid</arg>
<doc>Creates a sheet in the active workbook.</doc>
<status status="FAIL" start="2025-05-28T18:01:04.331060" elapsed="0.000000">SheetExistsAlreadyException: sheet `bookingid' already exists.</status>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Đã tạo sheet mới: bookingid</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-28T18:01:04.331060" elapsed="0.000000"/>
</kw>
<status status="FAIL" start="2025-05-28T18:01:04.331060" elapsed="0.000000">SheetExistsAlreadyException: sheet `bookingid' already exists.</status>
</branch>
<branch type="EXCEPT" assign="${error}">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.331060" level="INFO">Sheet bookingid có thể đã tồn tại: SheetExistsAlreadyException: sheet `bookingid' already exists.</msg>
<arg>Sheet bookingid có thể đã tồn tại: ${error}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:04.331060" elapsed="0.000000"/>
</kw>
<kw name="Switch Sheet" owner="ExcellentLibrary">
<arg>bookingid</arg>
<doc>Switches to the sheet with the supplied name within the active
workbook.</doc>
<status status="PASS" start="2025-05-28T18:01:04.331060" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.331060" level="INFO">Đã chuyển đến sheet: bookingid</msg>
<arg>Đã chuyển đến sheet: bookingid</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:04.331060" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-28T18:01:04.331060" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-28T18:01:04.331060" elapsed="0.000000"/>
</try>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.331060" level="INFO">Bắt đầu ghi dữ liệu vào sheet bookingid</msg>
<arg>Bắt đầu ghi dữ liệu vào sheet bookingid</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:04.331060" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A1</arg>
<arg>Field</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.331060" elapsed="0.004568"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B1</arg>
<arg>Value</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.335628" level="INFO">Đã ghi header vào sheet</msg>
<arg>Đã ghi header vào sheet</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.335628" level="INFO">${row} = 2</msg>
<var>${row}</var>
<arg>2</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A${row}</arg>
<arg>Booking ID</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B${row}</arg>
<arg>${booking_id}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.335628" level="INFO">${row} = 3</msg>
<var>${row}</var>
<arg>${row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A${row}</arg>
<arg>First Name</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B${row}</arg>
<arg>${booking_response}[firstname]</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.335628" level="INFO">${row} = 4</msg>
<var>${row}</var>
<arg>${row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A${row}</arg>
<arg>Last Name</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B${row}</arg>
<arg>${booking_response}[lastname]</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.335628" level="INFO">${row} = 5</msg>
<var>${row}</var>
<arg>${row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A${row}</arg>
<arg>Total Price</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B${row}</arg>
<arg>${booking_response}[totalprice]</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.335628" level="INFO">${row} = 6</msg>
<var>${row}</var>
<arg>${row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A${row}</arg>
<arg>Deposit Paid</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B${row}</arg>
<arg>${booking_response}[depositpaid]</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.335628" level="INFO">${row} = 7</msg>
<var>${row}</var>
<arg>${row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A${row}</arg>
<arg>Check-in Date</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B${row}</arg>
<arg>${booking_response}[bookingdates][checkin]</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.335628" level="INFO">${row} = 8</msg>
<var>${row}</var>
<arg>${row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A${row}</arg>
<arg>Check-out Date</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.335628" elapsed="0.005029"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B${row}</arg>
<arg>${booking_response}[bookingdates][checkout]</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.340657" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'additionalneeds' in ${booking_response}">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.340657" level="INFO">${row} = 9</msg>
<var>${row}</var>
<arg>${row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-28T18:01:04.340657" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A${row}</arg>
<arg>Additional Needs</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.341818" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B${row}</arg>
<arg>${booking_response}[additionalneeds]</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-28T18:01:04.341818" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-28T18:01:04.340657" elapsed="0.001161"/>
</branch>
<status status="PASS" start="2025-05-28T18:01:04.340657" elapsed="0.001161"/>
</if>
<kw name="Save" owner="ExcellentLibrary">
<doc>Saves the changes to the currently active workbook.</doc>
<status status="PASS" start="2025-05-28T18:01:04.341818" elapsed="0.008162"/>
</kw>
<kw name="Close Workbook" owner="ExcellentLibrary">
<doc>Closes the workbook identified by the supplied alias.</doc>
<status status="PASS" start="2025-05-28T18:01:04.349980" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-28T18:01:04.349980" level="INFO">Đã ghi thành công dữ liệu booking ID 1187 vào sheet 'bookingid'</msg>
<arg>Đã ghi thành công dữ liệu booking ID ${booking_id} vào sheet 'bookingid'</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-28T18:01:04.349980" elapsed="0.000000"/>
</kw>
<arg>${booking_data}</arg>
<arg>1187</arg>
<doc>Ghi dữ liệu booking vào Excel sheet mới</doc>
<status status="PASS" start="2025-05-28T18:01:04.324613" elapsed="0.025367"/>
</kw>
<kw name="Clean Workbooks" type="TEARDOWN">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Close All Workbooks" owner="ExcellentLibrary">
<doc>Closes all opened workbooks.</doc>
<status status="PASS" start="2025-05-28T18:01:04.349980" elapsed="0.000000"/>
</kw>
<arg>Close All Workbooks</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-05-28T18:01:04.349980" elapsed="0.000000"/>
</kw>
<doc>Đóng tất cả workbook đang mở</doc>
<status status="PASS" start="2025-05-28T18:01:04.349980" elapsed="0.000000"/>
</kw>
<doc>Lấy thông tin booking từ API và ghi vào Excel</doc>
<tag>excel-write</tag>
<tag>get-booking</tag>
<status status="PASS" start="2025-05-28T18:01:03.109312" elapsed="1.240668"/>
</test>
<kw name="Clean Workbooks" type="TEARDOWN">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Close All Workbooks" owner="ExcellentLibrary">
<doc>Closes all opened workbooks.</doc>
<status status="PASS" start="2025-05-28T18:01:04.349980" elapsed="0.000000"/>
</kw>
<arg>Close All Workbooks</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-05-28T18:01:04.349980" elapsed="0.000000"/>
</kw>
<doc>Đóng tất cả workbook đang mở</doc>
<status status="PASS" start="2025-05-28T18:01:04.349980" elapsed="0.000000"/>
</kw>
<doc>Test suite để kiểm thử API CreateBooking</doc>
<status status="PASS" start="2025-05-28T18:01:02.630225" elapsed="1.719755"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">excel-write</stat>
<stat pass="1" fail="0" skip="0">get-booking</stat>
</tag>
<suite>
<stat name="Create Booking Suite" id="s1" pass="1" fail="0" skip="0">Create Booking Suite</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
