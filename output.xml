<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.10.11 on win32)" generated="2025-05-16T21:02:20.151533" rpa="false" schemaversion="5">
<suite id="s1" name="Create Booking Data" source="C:\Users\<USER>\Documents\excel_robot_day5\create_booking_data.robot">
<test id="s1-t1" name="Create Booking Test Data Excel" line="10">
<kw name="Create Workbook" owner="ExcellentLibrary">
<arg>${EXCEL_FILE}</arg>
<arg>overwrite_file_if_exists=${TRUE}</arg>
<doc>Creates a new workbook and saves it to the given file path.</doc>
<status status="PASS" start="2025-05-16T21:02:20.509123" elapsed="0.023857"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A1</arg>
<arg>firstname</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B1</arg>
<arg>lastname</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>C1</arg>
<arg>totalprice</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>D1</arg>
<arg>depositpaid</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>E1</arg>
<arg>checkin_date</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>F1</arg>
<arg>checkout_date</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>G1</arg>
<arg>additionalneeds</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A2</arg>
<arg>John</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B2</arg>
<arg>Smith</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>C2</arg>
<arg>${150}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>D2</arg>
<arg>${TRUE}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>E2</arg>
<arg>2023-12-01</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>F2</arg>
<arg>2023-12-10</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>G2</arg>
<arg>Breakfast</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A3</arg>
<arg>Jane</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.532980" elapsed="0.008391"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B3</arg>
<arg>Doe</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.541371" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>C3</arg>
<arg>${200}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.541371" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>D3</arg>
<arg>${TRUE}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.541371" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>E3</arg>
<arg>2023-12-15</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.541371" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>F3</arg>
<arg>2023-12-20</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.541371" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>G3</arg>
<arg>Wifi</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.541371" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A4</arg>
<arg>Mike</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.541371" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B4</arg>
<arg>Brown</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>C4</arg>
<arg>${100}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>D4</arg>
<arg>${FALSE}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>E4</arg>
<arg>2024-01-05</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>F4</arg>
<arg>2024-01-10</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>G4</arg>
<arg>Extra pillow</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A5</arg>
<arg>Sarah</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B5</arg>
<arg>Johnson</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>C5</arg>
<arg>${300}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>D5</arg>
<arg>${TRUE}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>E5</arg>
<arg>2024-02-01</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>F5</arg>
<arg>2024-02-15</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>G5</arg>
<arg>Late checkout</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>A6</arg>
<arg>Robert</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>B6</arg>
<arg>Williams</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>C6</arg>
<arg>${250}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.543375" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>D6</arg>
<arg>${FALSE}</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.548853" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>E6</arg>
<arg>2024-03-10</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.548853" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>F6</arg>
<arg>2024-03-15</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.548853" elapsed="0.000000"/>
</kw>
<kw name="Write To Cell" owner="ExcellentLibrary">
<arg>G6</arg>
<arg>Airport transfer</arg>
<doc>Writes a value to the supplied cell.</doc>
<status status="PASS" start="2025-05-16T21:02:20.548853" elapsed="0.000000"/>
</kw>
<kw name="Save" owner="ExcellentLibrary">
<doc>Saves the changes to the currently active workbook.</doc>
<status status="PASS" start="2025-05-16T21:02:20.548853" elapsed="0.019207"/>
</kw>
<kw name="Close Workbook" owner="ExcellentLibrary">
<doc>Closes the workbook identified by the supplied alias.</doc>
<status status="PASS" start="2025-05-16T21:02:20.568060" elapsed="0.000000"/>
</kw>
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-05-16T21:02:20.568060" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Documents\excel_robot_day5\resources\booking_data.xlsx"&gt;C:\Users\<USER>\Documents\excel_robot_day5\resources\booking_data.xlsx&lt;/a&gt;' exists.</msg>
<arg>${EXCEL_FILE}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-05-16T21:02:20.568060" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-16T21:02:20.568060" level="INFO">File Excel đã được tạo thành công: C:\Users\<USER>\Documents\excel_robot_day5\resources\booking_data.xlsx</msg>
<arg>File Excel đã được tạo thành công: ${EXCEL_FILE}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-16T21:02:20.568060" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-16T21:02:20.509123" elapsed="0.058937"/>
</test>
<status status="PASS" start="2025-05-16T21:02:20.168058" elapsed="0.400002"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Create Booking Data" id="s1" pass="1" fail="0" skip="0">Create Booking Data</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
