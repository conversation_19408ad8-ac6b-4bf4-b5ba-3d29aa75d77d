*** Settings ***
Library    ExcellentLibrary
Library    OperatingSystem
Library    DateTime

*** Keywords ***
Clean Workbooks
    [Documentation]    Đóng tất cả workbook đang mở
    Run Keyword And Ignore Error    Close All Workbooks

*** Variables ***
${TEST_FILE}    ${CURDIR}${/}resources${/}employee_data.xlsx


*** Test Cases ***
Read Data From File
    [Documentation]    Minh họa cách đọc dữ liệu từ file excel
    # Clean Workbooks
    # Mở file excel
    Open Workbook    ${TEST_FILE}
    # Đọc dữ liệu từ ô A1
    ${value}=    Read From Cell    (1, 5)
    ${value}=    Read From Cell    A5
    # ${value}=    Read From Cell     a1    C2
    ${sheet_data}=    Read Sheet Data
    Log Many   ${sheet_data}   

    ${sheet1}=    Read Sheet Data    cell_range=A0:B3
    ${sheet2}=    Read Sheet Data    get_column_names_from_header_row=TRUE    cell_range=A0:B3
    Log Many   ${sheet1}
    Log Many   ${sheet2}


Test Write To Cell
    [Documentation]    <PERSON> họa cách ghi dữ liệu vào một ô
    Clean Workbooks
    # Tạo workbook mới
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Ghi dữ liệu vào các ô bằng cách sử dụng tọa độ A1
    Write To Cell    A1    Hello World
    Write To Cell    B1    123
    Write To Cell    C1    True

    # Ghi dữ liệu vào các ô bằng cách sử dụng tọa độ (hàng, cột)
    Write To Cell    coords:2,1    Row 2, Column 1
    Write To Cell    (2,2)    Row 2, Column 2

    # Lưu workbook
    Save

    # Đóng workbook
    Close Workbook

Test Write To Cell With Number Format
    [Documentation]    Minh họa cách ghi dữ liệu với định dạng số
    Clean Workbooks
    # Tạo workbook mới
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Ghi số với định dạng số thập phân
    Write To Cell    A1    ${1.234}    number_format=#.##

    # Ghi số với định dạng tiền tệ
    Write To Cell    A2    ${1000}    number_format=$#,##0.00

    # Ghi ngày tháng với định dạng ngày
    ${now}=    Get Current Date
    Write To Cell    A3    ${now}    number_format=yyyy-mm-dd

    # Lưu workbook
    Save

    # Đóng workbook
    Close Workbook

Test Read From Cell
    [Documentation]    Minh họa cách đọc dữ liệu từ một ô
    Clean Workbooks
    # Tạo workbook mới và ghi dữ liệu
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}
    Write To Cell    A1    Hello World
    Write To Cell    B1    123
    Write To Cell    C1    True

    # Đọc dữ liệu từ các ô bằng cách sử dụng tọa độ A1
    ${value1}=    Read From Cell    A1
    ${value2}=    Read From Cell    B1
    ${value3}=    Read From Cell    C1

    # In giá trị đọc được ra log
    Log    Giá trị ô A1: ${value1}
    Log    Giá trị ô B1: ${value2}
    Log    Giá trị ô C1: ${value3}

    # Đọc dữ liệu từ các ô bằng cách sử dụng tọa độ (hàng, cột)
    ${value4}=    Read From Cell    coords:1,1    # Tương đương A1
    ${value5}=    Read From Cell    (1,2)         # Tương đương B1

    # In giá trị đọc được ra log
    Log    Giá trị ô (1,1): ${value4}
    Log    Giá trị ô (1,2): ${value5}

    # Đóng workbook
    Close Workbook

Test Read From Cell With Trim
    [Documentation]    Minh họa cách đọc dữ liệu từ một ô và cắt khoảng trắng
    Clean Workbooks
    # Tạo workbook mới và ghi dữ liệu có khoảng trắng
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}
    Write To Cell    A1    ${SPACE}${SPACE}${SPACE}Hello World${SPACE}${SPACE}${SPACE}

    # Đọc dữ liệu không cắt khoảng trắng
    ${value1}=    Read From Cell    A1

    # Đọc dữ liệu và cắt khoảng trắng
    ${value2}=    Read From Cell    A1    trim=${TRUE}

    # In giá trị đọc được ra log
    Log    Giá trị không cắt khoảng trắng: "${value1}"
    Log    Giá trị đã cắt khoảng trắng: "${value2}"

    # Đóng workbook
    Close Workbook

Test Read Sheet Data
    [Documentation]    Minh họa cách đọc toàn bộ dữ liệu từ một sheet
    Clean Workbooks
    # Tạo workbook mới và ghi dữ liệu
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Tạo dữ liệu mẫu
    Write To Cell    A1    Name
    Write To Cell    B1    Age
    Write To Cell    C1    City

    Write To Cell    A2    John
    Write To Cell    B2    30
    Write To Cell    C2    New York

    Write To Cell    A3    Alice
    Write To Cell    B3    25
    Write To Cell    C3    London

    # Đọc toàn bộ dữ liệu từ sheet dưới dạng danh sách các danh sách
    ${data1}=    Read Sheet Data

    # In dữ liệu ra log
    Log    Dữ liệu dạng danh sách: ${data1}

    # Đọc dữ liệu với tên cột từ hàng đầu tiên
    ${data2}=    Read Sheet Data    get_column_names_from_header_row=${TRUE}

    # In dữ liệu ra log
    Log    Dữ liệu dạng từ điển với tên cột từ hàng đầu: ${data2}

    # Đóng workbook
    Close Workbook

Test Read Sheet Data With Column Names
    [Documentation]    Minh họa cách đọc dữ liệu sheet với tên cột tùy chỉnh
    Clean Workbooks
    # Tạo workbook mới và ghi dữ liệu
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Tạo dữ liệu mẫu (không có hàng tiêu đề)
    Write To Cell    A1    John
    Write To Cell    B1    30
    Write To Cell    C1    New York

    Write To Cell    A2    Alice
    Write To Cell    B2    25
    Write To Cell    C2    London

    # Đọc dữ liệu với tên cột tùy chỉnh
    @{column_names}=    Create List    Name    Age    City
    ${data}=    Read Sheet Data    column_names=${column_names}

    # In dữ liệu ra log
    Log    Dữ liệu với tên cột tùy chỉnh: ${data}

    # Đóng workbook
    Close Workbook

Test Read Sheet Data With Cell Range
    [Documentation]    Minh họa cách đọc dữ liệu từ một vùng ô cụ thể
    Clean Workbooks
    # Tạo workbook mới và ghi dữ liệu
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Tạo dữ liệu mẫu
    Write To Cell    A1    Name
    Write To Cell    B1    Age
    Write To Cell    C1    City

    Write To Cell    A2    John
    Write To Cell    B2    30
    Write To Cell    C2    New York

    Write To Cell    A3    Alice
    Write To Cell    B3    25
    Write To Cell    C3    London

    Write To Cell    A4    Bob
    Write To Cell    B4    35
    Write To Cell    C4    Paris

    # Đọc dữ liệu từ vùng ô A1:B3
    ${data}=    Read Sheet Data    cell_range=A1:B3

    # In dữ liệu ra log
    Log    Dữ liệu từ vùng A1:B3: ${data}

    # Đóng workbook
    Close Workbook

Test Read Sheet Data With Trim
    [Documentation]    Minh họa cách đọc dữ liệu và cắt khoảng trắng
    Clean Workbooks
    # Tạo workbook mới và ghi dữ liệu có khoảng trắng
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Tạo dữ liệu mẫu có khoảng trắng
    Write To Cell    A1    ${SPACE}${SPACE}${SPACE}Name${SPACE}${SPACE}${SPACE}
    Write To Cell    B1    ${SPACE}${SPACE}${SPACE}Age${SPACE}${SPACE}${SPACE}

    Write To Cell    A2    ${SPACE}${SPACE}${SPACE}John${SPACE}${SPACE}${SPACE}
    Write To Cell    B2    ${SPACE}${SPACE}${SPACE}30${SPACE}${SPACE}${SPACE}

    # Đọc dữ liệu và cắt khoảng trắng
    ${data}=    Read Sheet Data    trim=${TRUE}

    # In dữ liệu ra log
    Log    Dữ liệu đã cắt khoảng trắng: ${data}

    # Đóng workbook
    Close Workbook
