*** Settings ***
Library    ExcellentLibrary
Library    OperatingSystem

*** Keywords ***
Clean Workbooks
    [Documentation]    Đóng tất cả workbook đang mở
    Run Keyword And Ignore Error    Close All Workbooks

*** Variables ***
${TEST_FILE}    ${CURDIR}${/}resources${/}test_data.xlsx

*** Test Cases ***
Test Create Sheet
    [Documentation]    Minh họa cách tạo một sheet mới trong workbook
    Clean Workbooks
    # Tạo workbook mới
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Tạo sheet mới với tên chỉ định
    Create Sheet    Sheet2

    # Lưu workbook để áp dụng thay đổi
    Save

    # Đóng workbook
    Close Workbook

Test Switch Sheet
    [Documentation]    Minh họa cách chuyển đổi giữa các sheet
    Clean Workbooks
    # Tạo workbook mới
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Tạo thêm sheet mới
    Create Sheet    Sheet2
    Create Sheet    Sheet3

    # Viết dữ liệu vào sheet mặc định
    Write To Cell    A1    Test data

    # Chuyển đổi đến sheet mới tạo
    Switch Sheet    Sheet2
    # Thực hiện các thao tác trên Sheet2
    Write To Cell    A1    Data in Sheet2

    Switch Sheet    Sheet3
    # Thực hiện các thao tác trên Sheet3

    # Lưu và đóng workbook
    Save
    Close Workbook

Test Remove Sheet
    [Documentation]    Minh họa cách xóa một sheet
    Clean Workbooks
    # Tạo workbook mới
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Tạo thêm sheet mới
    Create Sheet    Sheet2
    Create Sheet    Sheet3

    # Xóa một sheet cụ thể
    Remove Sheet    Sheet2

    # Lưu và đóng workbook
    Save
    Close Workbook

Test Get Row Count
    [Documentation]    Minh họa cách lấy số lượng hàng trong sheet hiện tại
    Clean Workbooks
    # Tạo workbook mới
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Viết dữ liệu vào một số ô
    Write To Cell    A1    Value 1
    Write To Cell    A2    Value 2
    Write To Cell    A3    Value 3

    # Lấy số lượng hàng
    ${row_count}=    Get Row Count

    # In số lượng hàng ra log
    Log    Số lượng hàng: ${row_count}

    # Lưu và đóng workbook
    Save
    Close Workbook

Test Get Column Count
    [Documentation]    Minh họa cách lấy số lượng cột trong sheet hiện tại
    Clean Workbooks
    # Tạo workbook mới
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Viết dữ liệu vào một số ô
    Write To Cell    A1    Value 1
    Write To Cell    B1    Value 2
    Write To Cell    C1    Value 3

    # Lấy số lượng cột
    ${column_count}=    Get Column Count

    # In số lượng cột ra log
    Log    Số lượng cột: ${column_count}

    # Lưu và đóng workbook
    Save
    Close Workbook

Test Get Row Iterator
    [Documentation]    Minh họa cách lấy iterator để duyệt qua các hàng
    Clean Workbooks
    # Tạo workbook mới
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}

    # Viết dữ liệu vào một số ô
    Write To Cell    A1    Value 1
    Write To Cell    B1    Value 2
    Write To Cell    A2    Value 3
    Write To Cell    B2    Value 4

    # Lấy iterator để duyệt qua các hàng
    ${row_iterator}=    Get Row Iterator

    # Lưu ý: Việc sử dụng iterator trong Robot Framework không phổ biến
    # và thường được sử dụng trong các thư viện Python tùy chỉnh

    # Lưu và đóng workbook
    Save
    Close Workbook
