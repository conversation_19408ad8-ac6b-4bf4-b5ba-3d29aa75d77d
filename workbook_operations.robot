*** Settings ***
Library    ExcellentLibrary
Library    OperatingSystem

*** Keywords ***
Clean Workbooks
    [Documentation]    Đóng tất cả workbook đang mở
    Run Keyword And Ignore Error    Close All Workbooks

*** Variables ***
${TEST_FILE}    ${CURDIR}${/}resources${/}test_data.xlsx
${TEST_FILE2}   ${CURDIR}${/}resources${/}test_data2.xlsx

*** Test Cases ***
Test Create Workbook
    [Documentation]    Minh họa cách tạo một workbook Excel mới
    Clean Workbooks
    # Tạo một workbook mới và lưu vào đường dẫn chỉ định
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}
    # Kiểm tra xem file đã được tạo chưa
    File Should Exist    ${TEST_FILE}
    # Đóng workbook sau khi hoàn thành
    Close Workbook

Test Create Workbook With Alias
    [Documentation]    Minh họa cách tạo workbook với alias
    Clean Workbooks
    # Tạo workbook với alias để dễ dàng tham chiếu sau này
    Create Workbook    ${TEST_FILE2}    overwrite_file_if_exists=${TRUE}    alias=my_workbook
    # Kiểm tra xem file đã được tạo chưa
    File Should Exist    ${TEST_FILE2}
    # Đóng workbook bằng alias
    Close Workbook    my_workbook

Test Open Workbook
    [Documentation]    Minh họa cách mở một workbook Excel
    Clean Workbooks
    # Tạo workbook để test
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}
    Close Workbook

    # Mở workbook đã tạo
    Open Workbook    ${TEST_FILE}
    # Đóng workbook sau khi hoàn thành
    Close Workbook

Test Open Workbook With Alias
    [Documentation]    Minh họa cách mở workbook với alias
    Clean Workbooks
    # Tạo workbook để test
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}
    Close Workbook

    # Mở workbook với alias để dễ dàng tham chiếu
    Open Workbook    ${TEST_FILE}    alias=test_wb
    # Đóng workbook bằng alias
    Close Workbook    test_wb


Test Switch Workbook
    [Documentation]    Minh họa cách chuyển đổi giữa các workbook
    Clean Workbooks
    # Tạo và mở nhiều workbook
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}    alias=wb1

    # Thực hiện các thao tác trên wb1
    Write To Cell    A1    Data in wb1

    # Tạo workbook thứ hai
    Create Workbook    ${TEST_FILE2}    overwrite_file_if_exists=${TRUE}    alias=wb2

    # Thực hiện các thao tác trên wb2
    Write To Cell    A1    Data in wb2

    # Chuyển đổi về workbook đầu tiên
    Switch Workbook    wb1

    # Đóng từng workbook riêng lẻ
    Close Workbook    wb1
    Close Workbook    wb2

Test Log Opened Workbooks
    [Documentation]    Minh họa cách ghi log các workbook đang mở
    Clean Workbooks
    # Tạo và mở workbook
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}    alias=wb1

    # Ghi log các workbook đang mở vào log file
    Log Opened Workbooks    to_log=${TRUE}

    # Ghi log các workbook đang mở ra console
    Log Opened Workbooks    to_console=${TRUE}

    # Ghi log vào cả log file và console
    Log Opened Workbooks    to_log=${TRUE}    to_console=${TRUE}

    # Đóng workbook
    Close Workbook    wb1

Test Close Workbook
    [Documentation]    Minh họa cách đóng một workbook cụ thể
    Clean Workbooks
    # Tạo và mở nhiều workbook
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}    alias=wb1
    Create Workbook    ${TEST_FILE2}    overwrite_file_if_exists=${TRUE}    alias=wb2

    # Đóng một workbook cụ thể bằng alias
    Close Workbook    wb1

    # Đóng workbook đang active (không cần chỉ định alias)
    Close Workbook

Test Close All Workbooks
    [Documentation]    Minh họa cách đóng tất cả workbook đang mở
    Clean Workbooks
    # Tạo và mở workbook đầu tiên
    Create Workbook    ${TEST_FILE}    overwrite_file_if_exists=${TRUE}    alias=wb1

    # Lưu workbook đầu tiên
    Save

    # Tạo và mở workbook thứ hai
    Create Workbook    ${TEST_FILE2}    overwrite_file_if_exists=${TRUE}    alias=wb2

    # Lưu workbook thứ hai
    Save

    # Đóng tất cả workbook đang mở
    Run Keyword And Ignore Error    Close Workbook    wb1
    Run Keyword And Ignore Error    Close Workbook    wb2
